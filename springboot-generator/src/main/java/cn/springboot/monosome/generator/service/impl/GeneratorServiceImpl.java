package cn.springboot.monosome.generator.service.impl;

import cn.springboot.monosome.generator.service.GeneratorService;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 代码生成器服务实现类
 */
@Service
public class GeneratorServiceImpl implements GeneratorService {

    @Override
    public void generate(Map<String, Object> params, HttpServletResponse response) {
        // 这里实现代码生成逻辑
        // 可以根据表名生成Entity、Mapper、Service、Controller等代码
        System.out.println("生成代码参数: " + params);
    }

    @Override
    public Object getTables() {
        // 这里实现获取数据库表列表的逻辑
        List<String> tables = new ArrayList<>();
        tables.add("admin_user");
        tables.add("admin_role");
        tables.add("admin_permission");
        tables.add("user");
        tables.add("product");
        return tables;
    }
} 