package cn.springboot.monosome.generator.controller;

import cn.springboot.monosome.common.result.Result;
import cn.springboot.monosome.generator.service.GeneratorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 代码生成器控制器
 */
@Tag(name = "代码生成器", description = "代码生成相关接口")
@RestController
@RequestMapping("/generator")
public class GeneratorController {

    @Autowired
    private GeneratorService generatorService;

    @Operation(summary = "生成代码")
    @PostMapping("/generate")
    public void generate(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        generatorService.generate(params, response);
    }

    @Operation(summary = "获取数据库表列表")
    @GetMapping("/tables")
    public Result<Object> getTables() {
        return Result.success(generatorService.getTables());
    }
} 