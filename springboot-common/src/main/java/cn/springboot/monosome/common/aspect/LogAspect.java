package cn.springboot.monosome.common.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

/**
 * AOP日志切面
 */
@Slf4j
@Aspect
@Component
public class LogAspect {

    @Pointcut("execution(* cn.springboot.monosome..*.controller..*.*(..))")
    public void controllerLog() {}

    @Around("controllerLog()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        
        // 记录请求信息
        log.info("=== 请求开始 ===");
        log.info("请求URL: {}", request.getRequestURL().toString());
        log.info("请求方法: {}", request.getMethod());
        log.info("请求IP: {}", request.getRemoteAddr());
        log.info("请求类方法: {}.{}", joinPoint.getSignature().getDeclaringTypeName(), joinPoint.getSignature().getName());
        log.info("请求参数: {}", Arrays.toString(joinPoint.getArgs()));
        
        Object result = null;
        try {
            result = joinPoint.proceed();
            long endTime = System.currentTimeMillis();
            log.info("响应结果: {}", result);
            log.info("请求耗时: {}ms", endTime - startTime);
            log.info("=== 请求结束 ===");
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("请求异常: {}", e.getMessage());
            log.error("请求耗时: {}ms", endTime - startTime);
            log.error("=== 请求异常结束 ===");
            throw e;
        }
        
        return result;
    }
} 