package cn.springboot.monosome.common.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel工具类
 */
public class ExcelUtil {

    /**
     * 导出Excel
     */
    public static <T> void exportExcel(HttpServletResponse response, String fileName, 
                                     String sheetName, List<T> dataList, Class<T> clazz) {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

            // 设置表头样式
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            headWriteCellStyle.setFillForegroundColor((short) 22);
            headWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);

            // 设置内容样式
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();

            // 组合表头和内容样式
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = 
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

            EasyExcel.write(response.getOutputStream(), clazz)
                    .sheet(sheetName)
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .doWrite(dataList);
        } catch (IOException e) {
            throw new RuntimeException("导出Excel失败", e);
        }
    }

    /**
     * 导入Excel
     */
    public static <T> List<T> importExcel(MultipartFile file, Class<T> clazz) {
        try {
            List<T> dataList = new ArrayList<>();
            EasyExcel.read(file.getInputStream(), clazz, new AnalysisEventListener<T>() {
                @Override
                public void invoke(T data, AnalysisContext context) {
                    dataList.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    // 解析完成后的操作
                }
            }).sheet().doRead();
            return dataList;
        } catch (IOException e) {
            throw new RuntimeException("导入Excel失败", e);
        }
    }

    /**
     * 导入Excel（带验证）
     */
    public static <T> List<T> importExcelWithValidation(MultipartFile file, Class<T> clazz) {
        try {
            List<T> dataList = new ArrayList<>();
            EasyExcel.read(file.getInputStream(), clazz, new AnalysisEventListener<T>() {
                @Override
                public void invoke(T data, AnalysisContext context) {
                    // 这里可以添加数据验证逻辑
                    dataList.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    // 解析完成后的操作
                }
            }).sheet().doRead();
            return dataList;
        } catch (IOException e) {
            throw new RuntimeException("导入Excel失败", e);
        }
    }
} 