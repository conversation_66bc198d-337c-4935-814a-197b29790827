#!/bin/bash

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

# 应用名称
APP_NAME="springboot-admin"
PID_FILE="${APP_NAME}.pid"

# 检查PID文件是否存在
if [ ! -f "$PID_FILE" ]; then
    echo "错误: PID文件 $PID_FILE 不存在"
    exit 1
fi

# 读取PID
PID=$(cat "$PID_FILE")

# 检查进程是否存在
if ! ps -p "$PID" > /dev/null 2>&1; then
    echo "应用未在运行"
    rm -f "$PID_FILE"
    exit 1
fi

# 停止应用
echo "正在停止 $APP_NAME (PID: $PID)..."
kill "$PID"

# 等待进程结束
for i in {1..30}; do
    if ! ps -p "$PID" > /dev/null 2>&1; then
        echo "应用已停止"
        rm -f "$PID_FILE"
        exit 0
    fi
    sleep 1
done

# 强制杀死进程
echo "强制停止应用..."
kill -9 "$PID"
rm -f "$PID_FILE"
echo "应用已强制停止" 