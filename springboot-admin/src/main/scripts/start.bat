@echo off
setlocal

REM 获取脚本所在目录
set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

REM 应用名称
set APP_NAME=springboot-admin
set JAR_FILE=%APP_NAME%-1.0.0.jar
set PID_FILE=%APP_NAME%.pid

REM 检查JAR文件是否存在
if not exist "%JAR_FILE%" (
    echo 错误: JAR文件 %JAR_FILE% 不存在
    exit /b 1
)

REM 检查应用是否已经运行
if exist "%PID_FILE%" (
    for /f "tokens=*" %%i in (%PID_FILE%) do set PID=%%i
    tasklist /FI "PID eq !PID!" 2>NUL | find /I "!PID!" >NUL
    if not errorlevel 1 (
        echo 应用已经在运行中，PID: !PID!
        exit /b 1
    ) else (
        del "%PID_FILE%"
    )
)

REM 启动应用
echo 正在启动 %APP_NAME%...
start /b java -jar -Dspring.config.location=config/application.yml "%JAR_FILE%" > app.log 2>&1

REM 保存PID
for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq java.exe" /FO CSV ^| find "%JAR_FILE%"') do set PID=%%i
echo !PID! > "%PID_FILE%"
echo 应用已启动，PID: !PID!
echo 日志文件: app.log 