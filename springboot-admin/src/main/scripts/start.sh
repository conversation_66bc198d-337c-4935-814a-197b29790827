#!/bin/bash

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

# 应用名称
APP_NAME="springboot-admin"
JAR_FILE="${APP_NAME}-1.0.0.jar"
PID_FILE="${APP_NAME}.pid"

# 检查JAR文件是否存在
if [ ! -f "$JAR_FILE" ]; then
    echo "错误: JAR文件 $JAR_FILE 不存在"
    exit 1
fi

# 检查应用是否已经运行
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p "$PID" > /dev/null 2>&1; then
        echo "应用已经在运行中，PID: $PID"
        exit 1
    else
        rm -f "$PID_FILE"
    fi
fi

# 启动应用
echo "正在启动 $APP_NAME..."
nohup java -jar -Dspring.config.location=config/application.yml "$JAR_FILE" > app.log 2>&1 &

# 保存PID
echo $! > "$PID_FILE"
echo "应用已启动，PID: $!"
echo "日志文件: app.log" 