@echo off
setlocal

REM 获取脚本所在目录
set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

REM 应用名称
set APP_NAME=springboot-admin
set PID_FILE=%APP_NAME%.pid

REM 检查PID文件是否存在
if not exist "%PID_FILE%" (
    echo 错误: PID文件 %PID_FILE% 不存在
    exit /b 1
)

REM 读取PID
for /f "tokens=*" %%i in (%PID_FILE%) do set PID=%%i

REM 检查进程是否存在
tasklist /FI "PID eq !PID!" 2>NUL | find /I "!PID!" >NUL
if errorlevel 1 (
    echo 应用未在运行
    del "%PID_FILE%"
    exit /b 1
)

REM 停止应用
echo 正在停止 %APP_NAME% (PID: !PID!)...
taskkill /PID !PID! /F

REM 等待进程结束
for /l %%i in (1,1,30) do (
    tasklist /FI "PID eq !PID!" 2>NUL | find /I "!PID!" >NUL
    if errorlevel 1 (
        echo 应用已停止
        del "%PID_FILE%"
        exit /b 0
    )
    timeout /t 1 /nobreak >NUL
)

REM 强制杀死进程
echo 强制停止应用...
taskkill /PID !PID! /F /T
del "%PID_FILE%"
echo 应用已强制停止 