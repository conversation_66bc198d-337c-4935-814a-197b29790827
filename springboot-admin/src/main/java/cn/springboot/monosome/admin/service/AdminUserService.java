package cn.springboot.monosome.admin.service;

import cn.springboot.monosome.admin.entity.AdminUser;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 管理员用户服务接口
 */
public interface AdminUserService extends IService<AdminUser> {
    
    /**
     * 管理员登录
     */
    String login(String username, String password);
    
    /**
     * 根据用户名查询用户
     */
    AdminUser getByUsername(String username);
} 