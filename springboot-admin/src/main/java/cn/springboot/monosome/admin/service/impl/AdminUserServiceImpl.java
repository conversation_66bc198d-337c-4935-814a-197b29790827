package cn.springboot.monosome.admin.service.impl;

import cn.hutool.crypto.digest.BCrypt;
import cn.springboot.monosome.admin.entity.AdminUser;
import cn.springboot.monosome.admin.mapper.AdminUserMapper;
import cn.springboot.monosome.admin.service.AdminUserService;
import cn.springboot.monosome.common.exception.BusinessException;
import cn.springboot.monosome.common.util.JwtUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 管理员用户服务实现类
 */
@Service
public class AdminUserServiceImpl extends ServiceImpl<AdminUserMapper, AdminUser> implements AdminUserService {

    @Autowired
    private JwtUtil jwtUtil;

    @Override
    public String login(String username, String password) {
        AdminUser adminUser = getByUsername(username);
        if (adminUser == null) {
            throw new BusinessException("用户不存在");
        }
        
        if (!BCrypt.checkpw(password, adminUser.getPassword())) {
            throw new BusinessException("密码错误");
        }
        
        if (adminUser.getStatus() != 1) {
            throw new BusinessException("用户已被禁用");
        }
        
        return jwtUtil.generateToken(username, "ADMIN");
    }

    @Override
    public AdminUser getByUsername(String username) {
        LambdaQueryWrapper<AdminUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AdminUser::getUsername, username);
        return getOne(wrapper);
    }
} 