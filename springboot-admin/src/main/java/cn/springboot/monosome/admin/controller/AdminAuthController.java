package cn.springboot.monosome.admin.controller;

import cn.springboot.monosome.admin.service.AdminUserService;
import cn.springboot.monosome.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * 管理员认证控制器
 */
@Tag(name = "管理员认证", description = "管理员登录相关接口")
@RestController
@RequestMapping("/admin/auth")
public class AdminAuthController {

    @Autowired
    private AdminUserService adminUserService;

    @Operation(summary = "管理员登录")
    @PostMapping("/login")
    public Result<String> login(@RequestParam @NotBlank String username,
                               @RequestParam @NotBlank String password) {
        String token = adminUserService.login(username, password);
        return Result.success("登录成功", token);
    }

    @Operation(summary = "获取当前管理员信息")
    @GetMapping("/info")
    public Result<Object> getInfo() {
        // 这里应该从JWT中获取用户信息
        return Result.success("获取成功", null);
    }
} 