package cn.springboot.monosome.admin;

import cn.springboot.monosome.admin.service.AdminUserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 后台管理模块测试类
 */
@SpringBootTest
class AdminApplicationTests {

    @Autowired
    private AdminUserService adminUserService;

    @Test
    void contextLoads() {
    }

    @Test
    void testAdminLogin() {
        try {
            String token = adminUserService.login("admin", "123456");
            System.out.println("管理员登录成功，Token: " + token);
        } catch (Exception e) {
            System.out.println("管理员登录失败: " + e.getMessage());
        }
    }
} 