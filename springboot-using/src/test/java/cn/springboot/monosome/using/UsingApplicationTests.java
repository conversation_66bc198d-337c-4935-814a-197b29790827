package cn.springboot.monosome.using;

import cn.springboot.monosome.using.entity.User;
import cn.springboot.monosome.using.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 用户端模块测试类
 */
@SpringBootTest
class UsingApplicationTests {

    @Autowired
    private UserService userService;

    @Test
    void contextLoads() {
    }

    @Test
    void testUserLogin() {
        try {
            String token = userService.login("user1", "123456");
            System.out.println("用户登录成功，Token: " + token);
        } catch (Exception e) {
            System.out.println("用户登录失败: " + e.getMessage());
        }
    }

    @Test
    void testUserRegister() {
        try {
            User user = new User();
            user.setUsername("testuser");
            user.setPassword("123456");
            user.setNickname("测试用户");
            user.setEmail("<EMAIL>");
            user.setPhone("13800138003");
            
            userService.register(user);
            System.out.println("用户注册成功");
        } catch (Exception e) {
            System.out.println("用户注册失败: " + e.getMessage());
        }
    }
} 