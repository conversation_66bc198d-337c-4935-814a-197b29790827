package cn.springboot.monosome.using.service.impl;

import cn.springboot.monosome.using.entity.Product;
import cn.springboot.monosome.using.mapper.ProductMapper;
import cn.springboot.monosome.using.service.ProductService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 产品服务实现类
 */
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {
} 