package cn.springboot.monosome.using.controller;

import cn.springboot.monosome.common.result.Result;
import cn.springboot.monosome.using.entity.User;
import cn.springboot.monosome.using.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 用户控制器
 */
@Tag(name = "用户管理", description = "用户相关接口")
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public Result<String> login(@RequestParam @NotBlank String username,
                               @RequestParam @NotBlank String password) {
        String token = userService.login(username, password);
        return Result.success("登录成功", token);
    }

    @Operation(summary = "用户注册")
    @PostMapping("/register")
    public Result<Void> register(@RequestBody @Valid User user) {
        userService.register(user);
        return Result.success("注册成功");
    }

    @Operation(summary = "获取当前用户信息")
    @GetMapping("/info")
    public Result<Object> getInfo() {
        // 这里应该从JWT中获取用户信息
        return Result.success("获取成功", null);
    }
} 