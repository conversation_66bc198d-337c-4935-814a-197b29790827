package cn.springboot.monosome.using.service;

import cn.springboot.monosome.using.entity.User;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 用户服务接口
 */
public interface UserService extends IService<User> {
    
    /**
     * 用户登录
     */
    String login(String username, String password);
    
    /**
     * 用户注册
     */
    void register(User user);
    
    /**
     * 根据用户名查询用户
     */
    User getByUsername(String username);
} 