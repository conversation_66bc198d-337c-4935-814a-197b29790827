# Spring Boot 后台开发脚手架

一个基于Spring Boot的多模块后台开发脚手架，专注于后端服务开发。

## 项目特性

- ✅ 集成Spring Boot常用开发组件集、公共配置、AOP日志等
- ✅ Maven多模块架构
- ✅ 集成MyBatis Plus快速MySQL的DAO操作，以及多数据源
- ✅ 集成代码生成器
- ✅ 集成Swagger/Knife4j，可自动生成API文档
- ✅ 集成JWT、Shiro权限控制
- ✅ 集成Redis缓存
- ✅ 集成HikariCP连接池，JDBC性能和慢查询检测
- ✅ 集成Spring Boot Admin，实时检测项目运行情况
- ✅ 使用Assembly Maven插件进行不同环境打包部署
- ✅ 包含常用工具类（Excel处理、Redis等）
- ✅ 包名为cn.springboot.monosome.xx
- ✅ 模块分为后台管理端与用户端（using）
- ✅ 后台管理端可以管理业务端（using）的数据
- ✅ 包含测试代码和SQL脚本

## 项目结构

```
springboot-scaffold/
├── springboot-common/          # 公共模块
├── springboot-admin/           # 后台管理模块
├── springboot-using/           # 用户端模块
├── springboot-generator/       # 代码生成器模块
├── springboot-admin-server/    # Spring Boot Admin服务器
├── sql/                        # SQL脚本
└── README.md                   # 项目说明
```

## 技术栈

- **Spring Boot**: 2.7.18
- **MyBatis Plus**: 3.5.3.1
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **JWT**: 0.11.5
- **Shiro**: 1.12.0
- **Knife4j**: 4.1.0
- **Druid**: 1.2.18
- **Hutool**: 5.8.20
- **EasyExcel**: 3.3.2
- **Spring Boot Admin**: 2.7.10

## 快速开始

### 环境要求

- JDK 8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 数据库初始化

1. 创建数据库：
```sql
CREATE DATABASE springboot_scaffold DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行SQL脚本：
```bash
mysql -u root -p springboot_scaffold < sql/init.sql
```

### 启动服务

1. 启动Spring Boot Admin服务器：
```bash
cd springboot-admin-server
mvn spring-boot:run
```

2. 启动后台管理模块：
```bash
cd springboot-admin
mvn spring-boot:run
```

3. 启动用户端模块：
```bash
cd springboot-using
mvn spring-boot:run
```

4. 启动代码生成器模块：
```bash
cd springboot-generator
mvn spring-boot:run
```

### 访问地址

- Spring Boot Admin: http://localhost:8081 (admin/123456)
- 后台管理API文档: http://localhost:8080/doc.html
- 用户端API文档: http://localhost:8082/doc.html
- 代码生成器API文档: http://localhost:8083/doc.html
- Druid监控: http://localhost:8080/druid (admin/123456)

### 测试账号

**管理员账号：**
- 用户名：admin
- 密码：123456

**用户账号：**
- 用户名：user1
- 密码：123456

## 模块说明

### springboot-common (公共模块)

包含所有模块共用的功能：
- 统一返回结果封装
- 全局异常处理
- AOP日志切面
- Redis配置和工具类
- MyBatis Plus配置
- JWT工具类
- Excel工具类

### springboot-admin (后台管理模块)

后台管理系统，包含：
- 管理员用户管理
- 角色权限管理
- 业务数据管理
- 系统监控

### springboot-using (用户端模块)

用户端业务系统，包含：
- 用户注册登录
- 产品管理
- 业务功能

### springboot-generator (代码生成器模块)

代码生成器，支持：
- 根据数据库表生成Entity、Mapper、Service、Controller
- 支持自定义模板
- 支持批量生成

### springboot-admin-server (Spring Boot Admin服务器)

Spring Boot Admin监控服务器，提供：
- 应用监控
- 健康检查
- 性能指标
- 日志查看

## 开发指南

### 添加新模块

1. 在根pom.xml中添加模块：
```xml
<modules>
    <module>your-module</module>
</modules>
```

2. 创建模块目录结构：
```
your-module/
├── src/main/java/cn/springboot/monosome/yourmodule/
├── src/main/resources/
└── pom.xml
```

3. 配置模块pom.xml，继承根pom.xml并添加依赖。

### 添加新功能

1. 在common模块中添加公共功能
2. 在对应业务模块中实现具体功能
3. 添加单元测试
4. 更新API文档

### 数据库设计规范

- 表名使用下划线命名法
- 字段名使用下划线命名法
- 主键统一使用id
- 创建时间和更新时间字段统一使用create_time和update_time
- 状态字段统一使用status，0表示禁用，1表示启用

## 部署说明

### 打包

```bash
mvn clean package
```

### 运行

```bash
# 后台管理模块
java -jar springboot-admin/target/springboot-admin-1.0.0.jar

# 用户端模块
java -jar springboot-using/target/springboot-using-1.0.0.jar

# 代码生成器模块
java -jar springboot-generator/target/springboot-generator-1.0.0.jar

# Spring Boot Admin服务器
java -jar springboot-admin-server/target/springboot-admin-server-1.0.0.jar
```

### 配置文件

配置文件支持多环境：
- application.yml (默认配置)
- application-dev.yml (开发环境)
- application-test.yml (测试环境)
- application-prod.yml (生产环境)

## 常见问题

### 1. 数据库连接失败

检查数据库配置：
- 数据库服务是否启动
- 数据库连接信息是否正确
- 数据库用户权限是否足够

### 2. Redis连接失败

检查Redis配置：
- Redis服务是否启动
- Redis连接信息是否正确
- Redis密码是否正确

### 3. 端口冲突

修改application.yml中的server.port配置。

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 许可证

Apache License 2.0

## 联系方式

如有问题，请提交Issue或联系开发者。 