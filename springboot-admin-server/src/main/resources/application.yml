server:
  port: 8081

spring:
  application:
    name: springboot-admin-server
  
  security:
    user:
      name: admin
      password: 123456

# Spring Boot Admin配置
spring:
  boot:
    admin:
      ui:
        title: Spring Boot Admin
        brand: <img src="assets/img/icon-spring-boot-admin.svg"><span>Spring Boot Admin</span>
        login-icon: <img src="assets/img/icon-spring-boot-admin.svg">
        favicon: assets/img/favicon.png
        favicon-danger: assets/img/favicon-danger.png
        remember-me-enabled: true
        navbar:
          brand: <img src="assets/img/icon-spring-boot-admin.svg"><span>Spring Boot Admin</span>
          brand-link: /
          color: #fff
          inverse: true
        sidebar:
          brand: <img src="assets/img/icon-spring-boot-admin.svg"><span>Spring Boot Admin</span>
          brand-link: /
          color: #fff
          inverse: true
        context-path: /
        title: Spring Boot Admin
        show-views:
          environment: always
          info: always
          details: always
          health: always
          metrics: always
          logfile: always
          jolokia: always
          threaddump: always
          heapdump: always
          auditevents: always
          httptrace: always
          scheduledtasks: always
          sessions: always
          flyway: always
          liquibase: always
          mappings: always
          configprops: always
          env: always
          beans: always
          configprops: always
          threaddump: always
          heapdump: always
          jolokia: always
          logfile: always
          mappings: always
          metrics: always
          scheduledtasks: always
          sessions: always
          shutdown: always
          flyway: always
          liquibase: always
          auditevents: always
          httptrace: always 